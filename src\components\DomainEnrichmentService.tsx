import { useEffect } from "preact/compat";

interface DomainEnrichmentResponse {
  success: boolean;
  data?: any;
  error?: string;
  inputElement: HTMLInputElement;
  domain: string;
  widgetId: string;
}

interface Props {
  widgetId: string;
  apiUrl?: string;
  debounceDelay?: number;
}

// Custom event names
const DOMAIN_ENRICHMENT_SUCCESS = 'domainEnrichmentSuccess';
const DOMAIN_ENRICHMENT_ERROR = 'domainEnrichmentError';
const DOMAIN_ENRICHMENT_LOADING = 'domainEnrichmentLoading';

export default function DomainEnrichmentService({ 
  widgetId, 
  apiUrl = '/api/scrapagent',
  debounceDelay = 1500 
}: Props) {
  
  useEffect(() => {
    // Store active timers for each input element
    const debounceTimers = new Map<HTMLInputElement, NodeJS.Timeout>();
    
    // API call function
    const fetchDomainData = async (inputElement: HTMLInputElement, domain: string) => {
      if (!domain.trim()) {
        return;
      }

      // Dispatch loading event
      const loadingEvent = new CustomEvent(DOMAIN_ENRICHMENT_LOADING, {
        detail: {
          inputElement,
          domain,
          widgetId,
          loading: true
        }
      });
      document.dispatchEvent(loadingEvent);

      try {
        const response = await fetch(`${apiUrl}?domain_url=${encodeURIComponent(domain)}`);
        
        if (!response.ok) {
          throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        // Dispatch success event
        const successEvent = new CustomEvent(DOMAIN_ENRICHMENT_SUCCESS, {
          detail: {
            success: true,
            data,
            inputElement,
            domain,
            widgetId
          } as DomainEnrichmentResponse
        });
        document.dispatchEvent(successEvent);
        
      } catch (err) {
        // Dispatch error event
        const errorEvent = new CustomEvent(DOMAIN_ENRICHMENT_ERROR, {
          detail: {
            success: false,
            error: err instanceof Error ? err.message : "Failed to fetch domain data",
            inputElement,
            domain,
            widgetId
          } as DomainEnrichmentResponse
        });
        document.dispatchEvent(errorEvent);
      } finally {
        // Dispatch loading complete event
        const loadingCompleteEvent = new CustomEvent(DOMAIN_ENRICHMENT_LOADING, {
          detail: {
            inputElement,
            domain,
            widgetId,
            loading: false
          }
        });
        document.dispatchEvent(loadingCompleteEvent);
      }
    };

    // Handle input changes with debouncing
    const handleInputChange = (inputElement: HTMLInputElement) => {
      const domain = inputElement.value;

      // Clear existing timer for this input
      const existingTimer = debounceTimers.get(inputElement);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      // Set new timer for debounced API call
      const timer = setTimeout(() => {
        if (domain.trim()) {
          fetchDomainData(inputElement, domain);
        }
        debounceTimers.delete(inputElement);
      }, debounceDelay);

      debounceTimers.set(inputElement, timer);
    };

    // Handle immediate API calls (blur, enter)
    const handleImmediateCall = (inputElement: HTMLInputElement) => {
      const domain = inputElement.value;
      
      // Clear debounce timer
      const existingTimer = debounceTimers.get(inputElement);
      if (existingTimer) {
        clearTimeout(existingTimer);
        debounceTimers.delete(inputElement);
      }

      if (domain.trim()) {
        fetchDomainData(inputElement, domain);
      }
    };

    // Event listeners
    const inputListener = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (target && target.dataset.widgetId === widgetId) {
        handleInputChange(target);
      }
    };

    const blurListener = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (target && target.dataset.widgetId === widgetId) {
        handleImmediateCall(target);
      }
    };

    const keydownListener = (event: KeyboardEvent) => {
      const target = event.target as HTMLInputElement;
      if (target && target.dataset.widgetId === widgetId && event.key === 'Enter') {
        handleImmediateCall(target);
      }
    };

    // Attach event listeners to document
    document.addEventListener('input', inputListener);
    document.addEventListener('blur', blurListener, true); // Use capture phase
    document.addEventListener('keydown', keydownListener);

    // Cleanup function
    return () => {
      // Clear all timers
      debounceTimers.forEach((timer) => clearTimeout(timer));
      debounceTimers.clear();

      // Remove event listeners
      document.removeEventListener('input', inputListener);
      document.removeEventListener('blur', blurListener, true);
      document.removeEventListener('keydown', keydownListener);
    };
  }, [widgetId, apiUrl, debounceDelay]);

  // This component renders nothing - it's purely functional
  return null;
}

// Export event names and types for consumers
export {
  DOMAIN_ENRICHMENT_SUCCESS,
  DOMAIN_ENRICHMENT_ERROR,
  DOMAIN_ENRICHMENT_LOADING,
  type DomainEnrichmentResponse
};
