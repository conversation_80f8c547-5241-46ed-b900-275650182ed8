import clsx from "clsx";
import { useRef, useState, useEffect } from "preact/compat";
import { WidgetDataDto, WidgetDto } from "../dtos/WidgetDto";
import ErrorBanner from "./ui/banners/ErrorBanner";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import RefreshIcon from "./ui/icons/RefreshIcon";
import DomainEnrichmentWidget from "./DomainEnrichmentWidget";

interface Props {
  widget?: WidgetDto;
  widgetData?: WidgetDataDto;
  className?: string;
  size?: "sm" | "md" | "lg" | "full";
  isPreview?: boolean;
  isDarkMode?: boolean;
  error?: string;
  disabled?: boolean;
  canSubmit?: boolean;
  onTestWidget: (message: string) => void;
  onClose?: () => void;
  onReset?: () => void;
}

export default function WidgetComponent({
  widget,
  widgetData,
  className = "h-[calc(100vh-100px)]",
  size = "lg",
  isDarkMode,
  error,
  disabled,
  canSubmit,
  onTestWidget,
  onClose,
  onReset,
}: Props) {
  const [darkMode] = useState<boolean>(isDarkMode || false);
  const [themeColor] = useState<string>(widget?.appearance.theme || "zinc");
  const [inputMessage, setInputMessage] = useState<string>("");

  const mainInput = useRef<HTMLInputElement>(null);

  function onSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!canSubmit) {
      return;
    }
    setInputMessage("");
    onTestWidget(inputMessage);
  }

  return (
    <div className={clsx(darkMode && "dark")}>
      <form onSubmit={onSubmit} className={clsx(className, "bg-background text-foreground relative flex flex-col overflow-hidden", `theme-${themeColor}`)}>
        {widget && (
          <div
            className={clsx("border-border border-b p-1", size === "full" && "px-4", size === "lg" && "px-4", size === "md" && "px-2", size === "sm" && "px-2")}
          >
            <div className="flex items-center justify-between space-x-2">
              <div>
                {widget && (
                  <div className="flex items-center space-x-2">
                    {widget.appearance.logo && <img src={widget.appearance.logo} alt={widget.name} className="h-8 w-8" />}
                    {widget.name && (
                      <div
                        className={clsx(
                          "text-foreground py-2 text-center font-medium",
                          size === "full" && "text-base",
                          size === "lg" && "text-base",
                          size === "md" && "text-base",
                          size === "sm" && "text-sm"
                        )}
                      >
                        {widget.name}
                      </div>
                    )}
                  </div>
                )}
              </div>
              {(onReset || onClose) && (
                <div className="flex items-center space-x-2 flex-shrink-0">
                  {onReset && (
                    <div>
                      <button
                        type="button"
                        onClick={onReset}
                        className={clsx(
                          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                          "hover:bg-accent hover:text-accent-foreground",
                          "px-1 py-1"
                        )}
                      >
                        <RefreshIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                  {onClose && (
                    <div>
                      <button
                        type="button"
                        onClick={onClose}
                        className={clsx(
                          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                          "hover:bg-accent hover:text-accent-foreground",
                          "px-1 py-1"
                        )}
                      >
                        <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
        <div className={clsx("mx-auto w-full flex-grow overflow-auto px-4 py-2", size !== "full" && "lg:max-w-[40rem] xl:max-w-[48rem]")}>
          <div className="flex-1 space-y-2">
            <div className="relative flex items-center">
              <Input
                ref={mainInput}
                disabled={disabled}
                autoFocus
                type="text"
                name="message"
                id="message"
                autoComplete="off"
                placeholder={`Send "test" or "test-error"`}
                value={inputMessage}
                className="h-11"
                onChange={(e) => setInputMessage(e.target.value)}
                required
              />
              <div className="absolute inset-y-0 right-1 top-1 flex">
                <Button type="submit">Test</Button>
              </div>
            </div>
            <div className="text-lg font-bold bg-secondary border border-border rounded-md p-4">DATA: {JSON.stringify({ widgetData })}</div>

            {error && (
              <div className={clsx("mx-auto w-full overflow-auto pb-2", size !== "full" && "lg:max-w-[40rem] xl:max-w-[48rem]", size === "full" && "px-4")}>
                <ErrorBanner title="Error" text={error} />
              </div>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
