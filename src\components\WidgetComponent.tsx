import clsx from "clsx";
import { useRef, useState, useEffect } from "preact/compat";
import { WidgetDataDto, WidgetDto } from "../dtos/WidgetDto";
import ErrorBanner from "./ui/banners/ErrorBanner";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import RefreshIcon from "./ui/icons/RefreshIcon";

interface Props {
  widget?: WidgetDto;
  widgetData?: WidgetDataDto;
  className?: string;
  size?: "sm" | "md" | "lg" | "full";
  isPreview?: boolean;
  isDarkMode?: boolean;
  error?: string;
  disabled?: boolean;
  canSubmit?: boolean;
  onTestWidget: (message: string) => void;
  onClose?: () => void;
  onReset?: () => void;
}

export default function WidgetComponent({
  widget,
  widgetData,
  className = "h-[calc(100vh-100px)]",
  size = "lg",
  isDarkMode,
  error,
  disabled,
  canSubmit,
  onTestWidget,
  onClose,
  onReset,
}: Props) {
  const [darkMode] = useState<boolean>(isDarkMode || false);
  const [themeColor] = useState<string>(widget?.appearance.theme || "zinc");
  const [inputMessage, setInputMessage] = useState<string>("");
  const [activeTab, setActiveTab] = useState<"test" | "domain">("domain");

  // Domain enrichment states
  const [domain, setDomain] = useState<string>("");
  const [domainLoading, setDomainLoading] = useState<boolean>(false);
  const [domainResponse, setDomainResponse] = useState<any>(null);
  const [domainError, setDomainError] = useState<string>("");
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const mainInput = useRef<HTMLInputElement>(null);

  function onSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!canSubmit) {
      return;
    }
    setInputMessage("");
    onTestWidget(inputMessage);
  }

  // Domain enrichment function
  const fetchDomainData = async (domainUrl: string) => {
    if (!domainUrl.trim()) {
      setDomainResponse(null);
      return;
    }

    setDomainLoading(true);
    setDomainError("");

    try {
      // Use the proxy path instead of direct API call
      const response = await fetch(`/api/scrapagent?domain_url=${encodeURIComponent(domainUrl)}`);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setDomainResponse(data);

    } catch (err) {
      console.error('Domain API Error:', err);
      setDomainError(err instanceof Error ? err.message : "Failed to fetch domain data. Make sure your API at localhost:7071 is running.");
      setDomainResponse(null);
    } finally {
      setDomainLoading(false);
    }
  };

  // Handle domain input change with debouncing
  const handleDomainChange = (value: string) => {
    setDomain(value);

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer for debounced API call (1.5 seconds after user stops typing)
    const timer = setTimeout(() => {
      if (value.trim()) {
        fetchDomainData(value);
      } else {
        setDomainResponse(null);
        setDomainError("");
      }
    }, 1500); // 1.5 second delay

    setDebounceTimer(timer);
  };

  const handleDomainBlur = () => {
    // Clear debounce timer and call immediately on blur
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    fetchDomainData(domain);
  };

  const handleDomainKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Clear debounce timer and call immediately on Enter
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      fetchDomainData(domain);
    }
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return (
    <div className={clsx(darkMode && "dark")}>
      <form onSubmit={onSubmit} className={clsx(className, "bg-background text-foreground relative flex flex-col overflow-hidden", `theme-${themeColor}`)}>
        {widget && (
          <div
            className={clsx("border-border border-b p-1", size === "full" && "px-4", size === "lg" && "px-4", size === "md" && "px-2", size === "sm" && "px-2")}
          >
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-4">
                {widget && (
                  <div className="flex items-center space-x-2">
                    {widget.appearance.logo && <img src={widget.appearance.logo} alt={widget.name} className="h-8 w-8" />}
                    {widget.name && (
                      <div
                        className={clsx(
                          "text-foreground py-2 text-center font-medium",
                          size === "full" && "text-base",
                          size === "lg" && "text-base",
                          size === "md" && "text-base",
                          size === "sm" && "text-sm"
                        )}
                      >
                        {widget.name}
                      </div>
                    )}
                  </div>
                )}
              </div>
              {(onReset || onClose) && (
                <div className="flex items-center space-x-2 flex-shrink-0">
                  {onReset && (
                    <div>
                      <button
                        type="button"
                        onClick={onReset}
                        className={clsx(
                          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                          "hover:bg-accent hover:text-accent-foreground",
                          "px-1 py-1"
                        )}
                      >
                        <RefreshIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                  {onClose && (
                    <div>
                      <button
                        type="button"
                        onClick={onClose}
                        className={clsx(
                          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                          "hover:bg-accent hover:text-accent-foreground",
                          "px-1 py-1"
                        )}
                      >
                        <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-border">
          <div className="flex">
            <button
              type="button"
              onClick={() => setActiveTab("domain")}
              className={clsx(
                "px-4 py-2 text-sm font-medium border-b-2 transition-colors",
                activeTab === "domain"
                  ? "border-primary text-primary bg-primary/5"
                  : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
              )}
            >
              Domain Enrichment
            </button>
            {/* <button
              type="button"
              onClick={() => setActiveTab("test")}
              className={clsx(
                "px-4 py-2 text-sm font-medium border-b-2 transition-colors",
                activeTab === "test"
                  ? "border-primary text-primary bg-primary/5"
                  : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
              )}
            >
              Test Widget
            </button> */}
          </div>
        </div>

        {/* Content Area */}
        {activeTab === "domain" ? (
          <div className="flex-1 overflow-hidden flex flex-col">
            <div className="border-border border-b p-4">
              <div className="space-y-4">
                <div className="relative">
                  <Input
                    type="text"
                    placeholder="Enter domain (e.g., google.com)"
                    value={domain}
                    onChange={(e) => setDomain(e.target.value)}
                    onBlur={handleDomainBlur}
                    onKeyDown={handleDomainKeyDown}
                    className="w-full"
                    disabled={domainLoading}
                  />
                  {domainLoading && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    </div>
                  )}
                </div>

                {domainError && (
                  <ErrorBanner title="Error" text={domainError} />
                )}
              </div>
            </div>

            <div className="flex-1 overflow-auto p-4">
              {domainResponse && (
                <div className="bg-secondary border border-border rounded-lg p-4">
                  <h3 className="text-sm font-semibold text-foreground mb-2">API Response:</h3>
                  <pre className="text-xs text-foreground bg-background p-3 rounded border overflow-auto max-h-96">
                    {JSON.stringify(domainResponse, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className={clsx("mx-auto w-full flex-grow overflow-auto px-4 py-2", size !== "full" && "lg:max-w-[40rem] xl:max-w-[48rem]")}>
            <div className="flex-1 space-y-2">
              <div className="relative flex items-center">
                <Input
                  ref={mainInput}
                  disabled={disabled}
                  autoFocus
                  type="text"
                  name="message"
                  id="message"
                  autoComplete="off"
                  placeholder={`Send "test" or "test-error"`}
                  value={inputMessage}
                  className="h-11"
                  onChange={(e) => setInputMessage(e.target.value)}
                  required
                />
                <div className="absolute inset-y-0 right-1 top-1 flex">
                  <Button type="submit">Test</Button>
                </div>
              </div>
              <div className="text-lg font-bold bg-secondary border border-border rounded-md p-4">DATA: {JSON.stringify({ widgetData })}</div>

              {error && (
                <div className={clsx("mx-auto w-full overflow-auto pb-2", size !== "full" && "lg:max-w-[40rem] xl:max-w-[48rem]", size === "full" && "px-4")}>
                  <ErrorBanner title="Error" text={error} />
                </div>
              )}
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
