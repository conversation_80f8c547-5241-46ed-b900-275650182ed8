import clsx from "clsx";
import { useState, useEffect } from "preact/compat";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import ErrorBanner from "./ui/banners/ErrorBanner";

interface ApolloOrganizationData {
  id?: string;
  name?: string;
  website_url?: string;
  blog_url?: string;
  angellist_url?: string;
  linkedin_url?: string;
  twitter_url?: string;
  facebook_url?: string;
  primary_phone?: {
    number?: string;
    source?: string;
  };
  languages?: string[];
  alexa_ranking?: number;
  phone?: string;
  linkedin_uid?: string;
  founded_year?: number;
  publicly_traded_symbol?: string;
  publicly_traded_exchange?: string;
  logo_url?: string;
  crunchbase_url?: string;
  primary_domain?: string;
  persona_counts?: {
    [key: string]: number;
  };
  industry?: string;
  keywords?: string[];
  estimated_num_employees?: number;
  snippets_loaded?: boolean;
  industry_tag_id?: string;
  retail_location_count?: number;
  raw_address?: string;
  street_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  owned_by_organization_id?: string;
  suborganizations?: any[];
  num_suborganizations?: number;
  seo_description?: string;
  short_description?: string;
  annual_revenue_printed?: string;
  annual_revenue_in_thousands_int?: number;
  total_funding_printed?: string;
  total_funding_in_thousands_int?: number;
  latest_funding_round_date?: string;
  latest_funding_stage?: string;
  funding_events?: any[];
  technology_names?: string[];
  current_technologies?: any[];
  account_id?: string;
  account?: any;
  departmental_head_count?: {
    [key: string]: number;
  };
  seo_description_source?: string;
  description_source?: string;
  organization_raw_address_id?: string;
  organization_address_id?: string;
  suggest_location_enrichment?: boolean;
  domain?: string;
  team_id?: string;
  typed_custom_fields?: {
    [key: string]: any;
  };
  organization_id?: string;
  source?: string;
  original_source?: string;
  organization_latest_funding_stage_cd?: string;
  organization_latest_funding_stage?: string;
  organization_num_current_positions?: number;
  latest_funding_round_date_2?: string;
  num_technologies?: number;
}

interface Props {
  className?: string;
  size?: "sm" | "md" | "lg" | "full";
  isDarkMode?: boolean;
}

export default function DomainEnrichmentWidget({
  className = "h-[calc(100vh-100px)]",
  size = "lg",
  isDarkMode = false,
}: Props) {
  const [domain, setDomain] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [organizationData, setOrganizationData] = useState<ApolloOrganizationData | null>(null);
  const [error, setError] = useState<string>("");
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Debounced API call function
  const fetchOrganizationData = async (domainUrl: string) => {
    if (!domainUrl.trim()) {
      setOrganizationData(null);
      return;
    }

    setLoading(true);
    setError("");

    try {
      const apiKey = import.meta.env.VITE_APOLLO_API_KEY;

      if (!apiKey || apiKey === 'your_apollo_api_key_here') {
        throw new Error("Apollo API key not configured. Please set VITE_APOLLO_API_KEY in your .env file.");
      }

      const response = await fetch(`https://api.apollo.io/api/v1/organizations/enrich?domain=${domainUrl}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'X-Api-Key': apiKey,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Invalid API key. Please check your Apollo API key configuration.");
        } else if (response.status === 429) {
          throw new Error("API rate limit exceeded. Please try again later.");
        } else {
          throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }
      }

      const data = await response.json();

      if (data.organization) {
        setOrganizationData(data.organization);
      } else {
        setError("No organization data found for this domain");
        setOrganizationData(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch organization data");
      setOrganizationData(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debouncing
  const handleDomainChange = (value: string) => {
    setDomain(value);

    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set new timer for debounced API call
    const timer = setTimeout(() => {
      fetchOrganizationData(value);
    }, 1000); // 1 second delay

    setDebounceTimer(timer);
  };

  // Handle blur event for immediate API call
  const handleBlur = () => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    fetchOrganizationData(domain);
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  const renderOrganizationData = () => {
    if (!organizationData) return null;

    return (
      <div className="space-y-4">
        <div className="bg-secondary border border-border rounded-lg p-4">
          <div className="flex items-start space-x-4">
            {organizationData.logo_url && (
              <img 
                src={organizationData.logo_url} 
                alt={organizationData.name || "Organization logo"} 
                className="w-16 h-16 object-contain rounded"
              />
            )}
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-foreground">
                {organizationData.name || "Unknown Organization"}
              </h3>
              {organizationData.short_description && (
                <p className="text-sm text-muted-foreground mt-1">
                  {organizationData.short_description}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            {organizationData.website_url && (
              <div>
                <span className="text-sm font-medium text-foreground">Website:</span>
                <a 
                  href={organizationData.website_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 ml-2"
                >
                  {organizationData.website_url}
                </a>
              </div>
            )}

            {organizationData.industry && (
              <div>
                <span className="text-sm font-medium text-foreground">Industry:</span>
                <span className="text-sm text-muted-foreground ml-2">{organizationData.industry}</span>
              </div>
            )}

            {organizationData.estimated_num_employees && (
              <div>
                <span className="text-sm font-medium text-foreground">Employees:</span>
                <span className="text-sm text-muted-foreground ml-2">{organizationData.estimated_num_employees}</span>
              </div>
            )}

            {organizationData.founded_year && (
              <div>
                <span className="text-sm font-medium text-foreground">Founded:</span>
                <span className="text-sm text-muted-foreground ml-2">{organizationData.founded_year}</span>
              </div>
            )}

            {organizationData.annual_revenue_printed && (
              <div>
                <span className="text-sm font-medium text-foreground">Revenue:</span>
                <span className="text-sm text-muted-foreground ml-2">{organizationData.annual_revenue_printed}</span>
              </div>
            )}

            {organizationData.primary_phone?.number && (
              <div>
                <span className="text-sm font-medium text-foreground">Phone:</span>
                <span className="text-sm text-muted-foreground ml-2">{organizationData.primary_phone.number}</span>
              </div>
            )}
          </div>

          {organizationData.street_address && (
            <div className="mt-4">
              <span className="text-sm font-medium text-foreground">Address:</span>
              <div className="text-sm text-muted-foreground ml-2">
                {organizationData.street_address}
                {organizationData.city && `, ${organizationData.city}`}
                {organizationData.state && `, ${organizationData.state}`}
                {organizationData.postal_code && ` ${organizationData.postal_code}`}
                {organizationData.country && `, ${organizationData.country}`}
              </div>
            </div>
          )}

          {organizationData.technology_names && organizationData.technology_names.length > 0 && (
            <div className="mt-4">
              <span className="text-sm font-medium text-foreground">Technologies:</span>
              <div className="flex flex-wrap gap-2 mt-2">
                {organizationData.technology_names.slice(0, 10).map((tech, index) => (
                  <span 
                    key={index}
                    className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={clsx(isDarkMode && "dark")}>
      <div className={clsx(className, "bg-background text-foreground relative flex flex-col overflow-hidden")}>
        <div className={clsx("border-border border-b p-4")}>
          <h2 className="text-lg font-semibold text-foreground mb-4">Domain Enrichment</h2>
          
          <div className="space-y-4">
            <div className="relative">
              <Input
                type="text"
                placeholder="Enter domain (e.g., google.com)"
                value={domain}
                onChange={(e) => handleDomainChange(e.target.value)}
                onBlur={handleBlur}
                className="w-full"
                disabled={loading}
              />
              {loading && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                </div>
              )}
            </div>

            {error && (
              <ErrorBanner title="Error" text={error} />
            )}
          </div>
        </div>

        <div className="flex-1 overflow-auto p-4">
          {renderOrganizationData()}
        </div>
      </div>
    </div>
  );
}
