# Domain Enrichment Service

An invisible, reusable API service that can be embedded in any form to automatically enrich domain input fields with company data.

## Features

- **Invisible Operation**: No UI elements, works completely in the background
- **Widget ID Based**: Activates only on input fields with matching widget ID
- **Multiple Input Support**: Can handle multiple input fields on the same page
- **Debounced API Calls**: Automatically calls API after user stops typing (1.5s delay)
- **Immediate Triggers**: Also responds to blur and Enter key events
- **Custom Events**: Provides response data through DOM events
- **Error Handling**: Graceful error handling with custom error events

## How It Works

1. The service attaches event listeners to the entire document
2. When an input field with `data-widget-id` matching your widget ID is detected, it monitors that field
3. As users type, it debounces the input and calls your API endpoint
4. Results are dispatched as custom DOM events that you can listen to

## Setup

### 1. Include the Widget Script

```html
<script src="/dist/embed.js" 
        data-api-url="http://localhost:3000" 
        data-widget-id="your-widget-id">
</script>
```

### 2. Add Widget ID to Input Fields

```html
<input 
    type="text" 
    id="company-domain" 
    name="company-domain"
    data-widget-id="your-widget-id"
    placeholder="Enter company domain"
>
```

### 3. Listen for Events

```javascript
// Success event
document.addEventListener('domainEnrichmentSuccess', function(event) {
    const { inputElement, data, domain, widgetId } = event.detail;
    console.log('Enrichment data received:', data);
});

// Error event
document.addEventListener('domainEnrichmentError', function(event) {
    const { inputElement, error, domain, widgetId } = event.detail;
    console.error('Enrichment error:', error);
});

// Loading event
document.addEventListener('domainEnrichmentLoading', function(event) {
    const { inputElement, loading, domain, widgetId } = event.detail;
    if (loading) {
        console.log('Loading data for:', domain);
    } else {
        console.log('Loading complete for:', domain);
    }
});
```

## Event Details

### domainEnrichmentSuccess
Fired when API call succeeds.
```javascript
{
    success: true,
    data: { /* API response data */ },
    inputElement: HTMLInputElement,
    domain: "example.com",
    widgetId: "your-widget-id"
}
```

### domainEnrichmentError
Fired when API call fails.
```javascript
{
    success: false,
    error: "Error message",
    inputElement: HTMLInputElement,
    domain: "example.com",
    widgetId: "your-widget-id"
}
```

### domainEnrichmentLoading
Fired when loading state changes.
```javascript
{
    inputElement: HTMLInputElement,
    domain: "example.com",
    widgetId: "your-widget-id",
    loading: true // or false
}
```

## API Endpoint

The service calls your API endpoint with the following format:
```
GET /api/scrapagent?domain_url=example.com
```

Expected response format:
```json
{
    "company_name": "Example Corp",
    "industry": "Technology",
    "employees": 1000,
    // ... other company data
}
```

## Configuration Options

You can customize the service behavior:

```javascript
// In your widget initialization
<DomainEnrichmentService 
    widgetId="your-widget-id"
    apiUrl="/api/scrapagent"
    debounceDelay={1500}  // milliseconds
/>
```

## Multiple Input Fields

The same widget can work with multiple input fields:

```html
<input data-widget-id="your-widget-id" placeholder="Company domain">
<input data-widget-id="your-widget-id" placeholder="Competitor domain">
<input data-widget-id="your-widget-id" placeholder="Partner domain">
```

Each will trigger independent API calls and events.

## Example Use Cases

1. **Lead Generation Forms**: Automatically enrich company information
2. **CRM Data Entry**: Auto-populate company details from domain
3. **Sales Prospecting**: Gather competitor intelligence
4. **Form Validation**: Verify domain exists and get company data
5. **Data Enrichment**: Enhance existing records with fresh data

## Error Handling

The service handles various error scenarios:
- Network failures
- API errors (4xx, 5xx responses)
- Invalid domains
- Timeout issues

All errors are dispatched as `domainEnrichmentError` events.

## Performance

- **Debounced**: Prevents excessive API calls while typing
- **Cached**: Browser may cache identical requests
- **Lightweight**: Minimal overhead, no UI rendering
- **Memory Efficient**: Cleans up timers and event listeners

## Browser Compatibility

Works in all modern browsers that support:
- Custom Events
- Fetch API
- ES6 features

## Security

- Uses CORS-compliant requests
- No sensitive data stored in browser
- Respects same-origin policy through proxy configuration
