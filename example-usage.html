<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Enrichment Service Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .loading {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .response-data {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            margin-top: 5px;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Domain Enrichment Service Example</h1>
    <p>This demonstrates how to use the invisible domain enrichment widget service in any form.</p>
    
    <form>
        <div class="form-group">
            <label for="company-domain">Company Domain:</label>
            <input 
                type="text" 
                id="company-domain" 
                name="company-domain"
                data-widget-id="cmdoay97t000j1fg1070b754d"
                placeholder="Enter company domain (e.g., google.com)"
            >
            <div class="status" id="company-domain-status"></div>
            <div class="response-data" id="company-domain-data" style="display: none;"></div>
        </div>

        <div class="form-group">
            <label for="competitor-domain">Competitor Domain:</label>
            <input 
                type="text" 
                id="competitor-domain" 
                name="competitor-domain"
                data-widget-id="cmdoay97t000j1fg1070b754d"
                placeholder="Enter competitor domain (e.g., microsoft.com)"
            >
            <div class="status" id="competitor-domain-status"></div>
            <div class="response-data" id="competitor-domain-data" style="display: none;"></div>
        </div>

        <div class="form-group">
            <label for="partner-domain">Partner Domain:</label>
            <input 
                type="text" 
                id="partner-domain" 
                name="partner-domain"
                data-widget-id="cmdoay97t000j1fg1070b754d"
                placeholder="Enter partner domain (e.g., apple.com)"
            >
            <div class="status" id="partner-domain-status"></div>
            <div class="response-data" id="partner-domain-data" style="display: none;"></div>
        </div>

        <button type="submit">Submit Form</button>
    </form>

    <!-- Include your widget script -->
    <script src="/dist/embed.js" 
            data-api-url="http://localhost:3000" 
            data-widget-id="cmdoay97t000j1fg1070b754d">
    </script>

    <script>
        // Listen for domain enrichment events
        document.addEventListener('domainEnrichmentLoading', function(event) {
            const { inputElement, loading } = event.detail;
            const statusElement = document.getElementById(inputElement.id + '-status');
            const dataElement = document.getElementById(inputElement.id + '-data');
            
            if (loading) {
                inputElement.className = 'loading';
                statusElement.textContent = 'Loading domain data...';
                statusElement.style.color = '#007bff';
                dataElement.style.display = 'none';
            } else {
                inputElement.classList.remove('loading');
            }
        });

        document.addEventListener('domainEnrichmentSuccess', function(event) {
            const { inputElement, data, domain } = event.detail;
            const statusElement = document.getElementById(inputElement.id + '-status');
            const dataElement = document.getElementById(inputElement.id + '-data');
            
            inputElement.className = 'success';
            statusElement.textContent = `✓ Successfully enriched data for ${domain}`;
            statusElement.style.color = '#28a745';
            
            dataElement.textContent = JSON.stringify(data, null, 2);
            dataElement.style.display = 'block';
            
            console.log('Domain enrichment success:', { inputElement, data, domain });
        });

        document.addEventListener('domainEnrichmentError', function(event) {
            const { inputElement, error, domain } = event.detail;
            const statusElement = document.getElementById(inputElement.id + '-status');
            const dataElement = document.getElementById(inputElement.id + '-data');
            
            inputElement.className = 'error';
            statusElement.textContent = `✗ Error enriching ${domain}: ${error}`;
            statusElement.style.color = '#dc3545';
            dataElement.style.display = 'none';
            
            console.error('Domain enrichment error:', { inputElement, error, domain });
        });

        // Example of how to access enrichment data programmatically
        function getEnrichmentData(inputId) {
            const dataElement = document.getElementById(inputId + '-data');
            if (dataElement.style.display !== 'none' && dataElement.textContent) {
                try {
                    return JSON.parse(dataElement.textContent);
                } catch (e) {
                    return null;
                }
            }
            return null;
        }

        // Example form submission handler
        document.querySelector('form').addEventListener('submit', function(event) {
            event.preventDefault();
            
            const companyData = getEnrichmentData('company-domain');
            const competitorData = getEnrichmentData('competitor-domain');
            const partnerData = getEnrichmentData('partner-domain');
            
            console.log('Form submission with enriched data:', {
                company: companyData,
                competitor: competitorData,
                partner: partnerData
            });
            
            alert('Check console for enriched form data!');
        });
    </script>
</body>
</html>
